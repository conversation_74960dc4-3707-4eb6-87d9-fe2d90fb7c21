import React, { useState, useRef, useEffect, useCallback } from "react";
import clsx from "clsx";
import styles from "./index.module.scss";

type ResizableSide = "top" | "right" | "bottom" | "left";

interface ResizableProps {
    top?: boolean;
    right?: boolean;
    bottom?: boolean;
    left?: boolean;
}

interface CollapsedState {
    top: boolean;
    right: boolean;
    bottom: boolean;
    left: boolean;
}

interface OriginalDimensions {
    width: number | string;
    height: number | string;
}

export interface ResizableContainerProps {
    children: React.ReactNode;
    className?: string;
    resizable?: ResizableProps;
    minWidth?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    maxHeight?: number | string;
    initialWidth?: number | string;
    initialHeight?: number | string;
}

const convertToPixels = (value: string | number, parentDimension: number): number => {
    if (typeof value === "number") {
        return value;
    }
    if (typeof value === "string") {
        if (value.endsWith("%")) {
            const percentage = parseFloat(value);
            if (!isNaN(percentage)) {
                return (parentDimension * percentage) / 100;
            }
        }
        if (value.endsWith("px")) {
            return parseFloat(value);
        }
    }
    const num = parseFloat(value as string);
    return isNaN(num) ? 0 : num;
};

function useResizable({
    initialWidth,
    initialHeight,
    minWidth,
    maxWidth,
    minHeight,
    maxHeight,
    containerRef,
}: {
    initialWidth?: number | string;
    initialHeight?: number | string;
    minWidth?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    maxHeight?: number | string;
    containerRef: React.RefObject<HTMLDivElement>;
}) {
    const [dimensions, setDimensions] = useState({
        width: initialWidth ?? "auto",
        height: initialHeight ?? "auto",
    });

    const [collapsed, setCollapsed] = useState<CollapsedState>({
        top: false,
        right: false,
        bottom: false,
        left: false,
    });

    const [originalDimensions, setOriginalDimensions] = useState<OriginalDimensions>({
        width: initialWidth ?? "auto",
        height: initialHeight ?? "auto",
    });

    const startDrag = useRef({
        startX: 0,
        startY: 0,
        startWidth: 0,
        startHeight: 0,
        side: "" as ResizableSide | "",
    });

    const getParentDimensions = useCallback(() => {
        if (containerRef.current?.parentElement) {
            const parentRect = containerRef.current.parentElement.getBoundingClientRect();
            return { parentWidth: parentRect.width, parentHeight: parentRect.height };
        }
        return { parentWidth: window.innerWidth, parentHeight: window.innerHeight };
    }, [containerRef]);

    useEffect(() => {
        const { parentWidth, parentHeight } = getParentDimensions();
        const newWidth = initialWidth !== undefined ? convertToPixels(initialWidth, parentWidth) : "auto";
        const newHeight = initialHeight !== undefined ? convertToPixels(initialHeight, parentHeight) : "auto";

        setDimensions({
            width: newWidth,
            height: newHeight,
        });
        setOriginalDimensions({
            width: newWidth,
            height: newHeight,
        });
    }, [initialWidth, initialHeight, getParentDimensions]);

    const handleCollapse = useCallback(
        (side: ResizableSide) => {
            if (!containerRef.current) return;

            // Store current dimensions as original before collapsing
            const currentWidth = containerRef.current.offsetWidth;
            const currentHeight = containerRef.current.offsetHeight;

            setOriginalDimensions({
                width: currentWidth,
                height: currentHeight,
            });

            // Collapse the container
            setCollapsed((prev) => ({ ...prev, [side]: true }));

            switch (side) {
                case "right":
                case "left":
                    setDimensions((prev) => ({ ...prev, width: 0 }));
                    break;
                case "top":
                case "bottom":
                    setDimensions((prev) => ({ ...prev, height: 0 }));
                    break;
            }
        },
        [containerRef]
    );

    const handleExpand = useCallback(
        (side: ResizableSide) => {
            setCollapsed((prev) => ({ ...prev, [side]: false }));

            // Restore original dimensions
            switch (side) {
                case "right":
                case "left":
                    setDimensions((prev) => ({ ...prev, width: originalDimensions.width }));
                    break;
                case "top":
                case "bottom":
                    setDimensions((prev) => ({ ...prev, height: originalDimensions.height }));
                    break;
            }
        },
        [originalDimensions]
    );

    const handleMouseDown = useCallback(
        (side: ResizableSide) => (e: React.MouseEvent<HTMLDivElement>) => {
            e.preventDefault();
            e.stopPropagation();

            const { parentWidth, parentHeight } = getParentDimensions();
            if (!containerRef.current) return;

            startDrag.current = {
                startX: e.clientX,
                startY: e.clientY,
                startWidth: containerRef.current.offsetWidth,
                startHeight: containerRef.current.offsetHeight,
                side: side,
            };

            const handleMouseMove = (event: MouseEvent) => {
                event.preventDefault();
                event.stopPropagation();

                let newWidth = startDrag.current.startWidth;
                let newHeight = startDrag.current.startHeight;

                const deltaX = event.clientX - startDrag.current.startX;
                const deltaY = event.clientY - startDrag.current.startY;

                switch (startDrag.current.side) {
                    case "right":
                        newWidth += deltaX;
                        break;
                    case "left":
                        newWidth -= deltaX;
                        break;

                    case "bottom":
                        newHeight += deltaY;
                        break;
                    case "top":
                        newHeight -= deltaY;
                        break;
                }

                const minW = minWidth !== undefined ? convertToPixels(minWidth, parentWidth) : 0;
                const maxW = maxWidth !== undefined ? convertToPixels(maxWidth, parentWidth) : Infinity;
                const minH = minHeight !== undefined ? convertToPixels(minHeight, parentHeight) : 0;
                const maxH = maxHeight !== undefined ? convertToPixels(maxHeight, parentHeight) : Infinity;

                newWidth = Math.max(minW, Math.min(newWidth, maxW));
                newHeight = Math.max(minH, Math.min(newHeight, maxH));

                requestAnimationFrame(() => {
                    setDimensions({ width: newWidth, height: newHeight });
                });
            };

            const handleMouseUp = () => {
                window.removeEventListener("mousemove", handleMouseMove);
                window.removeEventListener("mouseup", handleMouseUp);
            };

            window.addEventListener("mousemove", handleMouseMove);
            window.addEventListener("mouseup", handleMouseUp);
        },
        [getParentDimensions, minWidth, maxWidth, minHeight, maxHeight, containerRef]
    );

    return { dimensions, handleMouseDown, collapsed, handleCollapse, handleExpand };
}

export default function ResizableContainer({
    children,
    className,
    resizable = { top: true, right: true, bottom: true, left: true },
    minWidth = "10%",
    maxWidth = "100%",
    minHeight = "10%",
    maxHeight = "100%",
    initialWidth = "100%",
    initialHeight = "100%",
}: ResizableContainerProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const { dimensions, handleMouseDown, collapsed, handleCollapse, handleExpand } = useResizable({
        initialWidth,
        initialHeight,
        minWidth,
        maxWidth,
        minHeight,
        maxHeight,
        containerRef,
    });

    const style: React.CSSProperties = {
        position: "relative",
        transition: "width 0.3s ease, height 0.3s ease",
    };
    if (dimensions.width !== "auto") style.width = `${dimensions.width}px`;
    if (dimensions.height !== "auto") style.height = `${dimensions.height}px`;

    return (
        <div ref={containerRef} className={clsx(styles["resizable-container"], className)} style={style}>
            {children}

            {/* Resizers with collapse buttons */}
            {resizable.top && (
                <>
                    <div className={clsx(styles.resizer, styles.top)} onMouseDown={handleMouseDown("top")} />
                    {!collapsed.top && (
                        <button
                            className={clsx(styles["collapse-button"], styles["collapse-top"])}
                            onClick={() => handleCollapse("top")}
                            title="Collapse to top"
                        >
                            ▲
                        </button>
                    )}
                    {collapsed.top && (
                        <button
                            className={clsx(styles["restore-button"], styles["restore-from-top"])}
                            onClick={() => handleExpand("top")}
                            title="Expand from top"
                        >
                            ▼
                        </button>
                    )}
                </>
            )}

            {resizable.right && (
                <>
                    <div className={clsx(styles.resizer, styles.right)} onMouseDown={handleMouseDown("right")} />
                    {!collapsed.right && (
                        <button
                            className={clsx(styles["collapse-button"], styles["collapse-right"])}
                            onClick={() => handleCollapse("right")}
                            title="Collapse to right"
                        >
                            ▶
                        </button>
                    )}
                    {collapsed.right && (
                        <button
                            className={clsx(styles["restore-button"], styles["restore-from-right"])}
                            onClick={() => handleExpand("right")}
                            title="Expand from right"
                        >
                            ◀
                        </button>
                    )}
                </>
            )}

            {resizable.bottom && (
                <>
                    <div className={clsx(styles.resizer, styles.bottom)} onMouseDown={handleMouseDown("bottom")} />
                    {!collapsed.bottom && (
                        <button
                            className={clsx(styles["collapse-button"], styles["collapse-bottom"])}
                            onClick={() => handleCollapse("bottom")}
                            title="Collapse to bottom"
                        >
                            ▼
                        </button>
                    )}
                    {collapsed.bottom && (
                        <button
                            className={clsx(styles["restore-button"], styles["restore-from-bottom"])}
                            onClick={() => handleExpand("bottom")}
                            title="Expand from bottom"
                        >
                            ▲
                        </button>
                    )}
                </>
            )}

            {resizable.left && (
                <>
                    <div className={clsx(styles.resizer, styles.left)} onMouseDown={handleMouseDown("left")} />
                    {!collapsed.left && (
                        <button
                            className={clsx(styles["collapse-button"], styles["collapse-left"])}
                            onClick={() => handleCollapse("left")}
                            title="Collapse to left"
                        >
                            ◀
                        </button>
                    )}
                    {collapsed.left && (
                        <button
                            className={clsx(styles["restore-button"], styles["restore-from-left"])}
                            onClick={() => handleExpand("left")}
                            title="Expand from left"
                        >
                            ▶
                        </button>
                    )}
                </>
            )}
        </div>
    );
}

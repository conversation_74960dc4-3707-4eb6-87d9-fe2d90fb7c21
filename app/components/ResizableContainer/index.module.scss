.resizable-container {
    position: relative;
    display: flex;
    overflow: hidden;
}

.resizer {
    position: absolute;
    z-index: 10;
    cursor: ew-resize;
    transition: all ease 0.3s;

    &:hover,
    &:active {
        background-color: #0099f2;
    }
}

.top,
.bottom {
    height: 2px;
    left: 0;
    right: 0;
    cursor: ns-resize;
}

.left,
.right {
    width: 2px;
    top: 0;
    bottom: 0;
    cursor: ew-resize;
}

.top {
    top: -1px;
}

.bottom {
    bottom: -1px;
}

.left {
    left: -1px;
}

.right {
    right: -1px;
}

.collapse-button {
    position: absolute;
    z-index: 15;
    background: #0099f2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;

    &:hover {
        opacity: 1;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 153, 242, 0.3);
    }

    &:active {
        transform: scale(0.95);
    }
}

.collapse-top {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.collapse-bottom {
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.collapse-left {
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
}

.collapse-right {
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
}

/* Restore buttons */
.restore-button {
    position: absolute;
    z-index: 15;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;

    &:hover {
        opacity: 1;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    &:active {
        transform: scale(0.95);
    }
}

.restore-from-top {
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
}

.restore-from-bottom {
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
}

.restore-from-left {
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
}

.restore-from-right {
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
}

/* Show restore buttons on hover when collapsed */
.resizable-container:hover .restore-button {
    opacity: 0.7;
}
"use client";

import React from "react";
import ResizableContainer from "../components/ResizableContainer";

export default function TestResizablePage() {
    return (
        <div style={{ padding: "20px", height: "100vh", display: "flex", flexDirection: "column" }}>
            <h1>ResizableContainer with Collapse/Expand Test</h1>
            <p>
                This page demonstrates the enhanced ResizableContainer with collapse/expand functionality:
            </p>
            <ul>
                <li>Click the blue buttons on the edges to collapse the container in that direction</li>
                <li>When collapsed, hover over the container to see the green restore button</li>
                <li>Click the restore button to expand back to original size</li>
                <li>You can still drag the edges to resize as before</li>
            </ul>
            
            <div style={{ flex: 1, position: "relative", marginTop: "20px", border: "2px dashed #ccc" }}>
                <ResizableContainer
                    initialWidth="60%"
                    initialHeight="70%"
                    minWidth="100px"
                    minHeight="100px"
                    resizable={{ top: true, right: true, bottom: true, left: true }}
                    style={{
                        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: "white",
                        fontSize: "18px",
                        fontWeight: "bold",
                        textAlign: "center",
                        padding: "20px",
                        boxSizing: "border-box",
                    }}
                >
                    <div>
                        <h2>Resizable Content</h2>
                        <p>This container can be:</p>
                        <ul style={{ textAlign: "left", marginTop: "10px" }}>
                            <li>Resized by dragging edges</li>
                            <li>Collapsed using edge buttons</li>
                            <li>Expanded using restore buttons</li>
                        </ul>
                        <p style={{ marginTop: "20px", fontSize: "14px", opacity: 0.9 }}>
                            Try clicking the blue buttons on the edges!
                        </p>
                    </div>
                </ResizableContainer>
            </div>
        </div>
    );
}
